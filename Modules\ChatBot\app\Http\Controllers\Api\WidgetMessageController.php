<?php

namespace Modules\ChatBot\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\ChatBot\Http\Controllers\Controller;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\BotShareLink;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Enums\ContentType;
use Modules\ChatBot\Traits\ResponseTrait;
use Modules\ChatBot\Facades\MessageFacade;
use Illuminate\Support\Str;

class WidgetMessageController extends Controller
{
    use ResponseTrait;

    /**
     * Get messages for conversation.
     */
    public function index(Request $request, string $conversationUuid): JsonResponse
    {
        try {
            $conversation = Conversation::with(['bot'])
                                      ->where('uuid', $conversationUuid)
                                      ->active()
                                      ->firstOrFail();

            // Validate bot access
            $bot = $this->validateBotAccess($request, $conversation->bot->uuid);
            if (!$bot) {
                return $this->errorResponse('Bot access denied', 403);
            }

            // Get messages (latest first for widget)
            $messages = Message::where('conversation_id', $conversation->id)
                              ->where('status', MessageStatus::COMPLETED)
                              ->orderBy('created_at', 'asc')
                              ->orderBy('id', 'asc')
                              ->limit(100)
                              ->get()
                              ->map(function ($message) {
                                  return [
                                      'id' => $message->uuid,
                                      'conversationId' => $message->conversation->uuid,
                                      'type' => $message->role === MessageRole::USER ? 'user' : 'bot',
                                      'content' => $message->content,
                                      'timestamp' => $message->created_at,
                                      'metadata' => [
                                          'tokens' => $message->tokens_used,
                                          'model' => $message->model_used,
                                      ]
                                  ];
                              });

            return $this->successResponse([
                'messages' => $messages,
                'conversation' => [
                    'uuid' => $conversation->uuid,
                    'title' => $conversation->title,
                ],
                'pagination' => [
                    'total' => $messages->count(),
                    'limit' => 100,
                ]
            ], 'Messages retrieved successfully');

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Send message and get AI response.
     */
    public function sendMessage(Request $request, string $conversationUuid): JsonResponse
    {
        try {
            $request->validate([
                'message' => 'required|string|max:2000',
                'messageType' => 'nullable|string|in:text,quick_reply,postback',
                'attachments' => 'nullable|array|max:5',
                'attachments.*.type' => 'required_with:attachments|string',
                'attachments.*.url' => 'required_with:attachments|url',
                'attachments.*.name' => 'nullable|string',
                'metadata' => 'nullable|array',
            ]);

            $conversation = Conversation::with(['bot'])
                                      ->where('uuid', $conversationUuid)
                                      ->active()
                                      ->firstOrFail();

            // Validate bot access
            $bot = $this->validateBotAccess($request, $conversation->bot->uuid);
            if (!$bot) {
                return $this->errorResponse('Bot access denied', 403);
            }

            // Create user message
            $userMessage = Message::create([
                'uuid' => Str::uuid(),
                'conversation_id' => $conversation->id,
                'role' => MessageRole::USER,
                'content' => $request->message,
                'content_type' => ContentType::TEXT,
                'status' => MessageStatus::COMPLETED,
                'metadata' => array_merge([
                    'messageType' => $request->messageType ?? 'text',
                    'attachments' => $request->attachments ?? [],
                    'clientTimestamp' => $request->metadata['clientTimestamp'] ?? now()->timestamp,
                ], $request->metadata ?? []),
                'created_at' => now(),
            ]);

            // Update conversation last message time
            $conversation->update(['last_message_at' => now()]);

            // Generate AI response using existing service
            $aiMessage = MessageFacade::generateAIResponse($conversation, $userMessage);

            // Return both messages in widget format
            return $this->successResponse([
                'userMessage' => [
                    'id' => $userMessage->uuid,
                    'conversationId' => $conversation->uuid,
                    'type' => 'user',
                    'content' => $userMessage->content,
                    'timestamp' => $userMessage->created_at,
                ],
                'botMessage' => [
                    'id' => $aiMessage->uuid,
                    'conversationId' => $conversation->uuid,
                    'type' => 'bot',
                    'content' => $aiMessage->content,
                    'timestamp' => $aiMessage->created_at,
                    'metadata' => [
                        'tokens' => $aiMessage->tokens_used,
                        'model' => $aiMessage->model_used,
                        'responseTime' => $aiMessage->response_time,
                    ]
                ],
                'conversation' => [
                    'uuid' => $conversation->uuid,
                    'lastMessageAt' => $conversation->last_message_at,
                ]
            ], 'Message sent and response generated successfully', 201);

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Validate bot access for widget.
     */
    private function validateBotAccess(Request $request, string $botUuid): ?Bot
    {
        // Try API key access
        if ($request->hasHeader('Authorization')) {
            $apiKey = str_replace('Bearer ', '', $request->header('Authorization'));
            
            if (str_starts_with($apiKey, 'pk_')) {
                $bot = Bot::where('uuid', $botUuid)
                         ->where('api_key', $apiKey)
                         ->active()
                         ->first();
                
                if ($bot) {
                    return $bot;
                }
            }
        }

        // Try share token access
        $shareLink = BotShareLink::with(['bot'])
                                ->where('token', $botUuid)
                                ->active()
                                ->first();

        if ($shareLink && $shareLink->bot->canBeShared()) {
            return $shareLink->bot;
        }

        return null;
    }
}
