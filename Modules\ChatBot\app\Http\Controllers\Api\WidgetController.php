<?php

namespace Modules\ChatBot\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\BotShareLink;
use Modules\Core\Traits\ResponseTrait;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class WidgetController extends Controller
{
    use ResponseTrait;

    /**
     * Get bot configuration for widget (public access with API key or share token).
     */
    public function getBotConfig(Request $request, string $identifier): JsonResponse
    {
        try {
            $bot = null;
            $accessType = 'unknown';

            // Try to get bot by UUID first (API key access)
            if ($request->hasHeader('Authorization')) {
                $apiKey = str_replace('Bearer ', '', $request->header('Authorization'));
                
                // Validate API key format
                if (str_starts_with($apiKey, 'pk_')) {
                    $bot = Bot::where('uuid', $identifier)
                             ->where('api_key', $apiKey)
                             ->active()
                             ->first();
                    
                    if ($bot) {
                        $accessType = 'api_key';
                    }
                }
            }

            // If not found by API key, try share token
            if (!$bot) {
                $shareLink = BotShareLink::with(['bot'])
                                       ->where('token', $identifier)
                                       ->active()
                                       ->first();

                if ($shareLink && $shareLink->bot->canBeShared()) {
                    $bot = $shareLink->bot;
                    $accessType = 'share_token';
                }
            }

            if (!$bot) {
                return $this->errorResponse('Bot not found or access denied', 404);
            }

            // Return widget-optimized bot configuration
            return $this->successResponse([
                'uuid' => $bot->uuid,
                'name' => $bot->name,
                'description' => $bot->description,
                'logoUrl' => $bot->logo_url,
                'greetingMessage' => $bot->greeting_message ?: "Xin chào! Tôi là {$bot->name}. Tôi có thể giúp gì cho bạn?",
                'starterMessages' => $bot->starter_messages ?: [],
                'closingMessage' => $bot->closing_message,
                'status' => $bot->status->value,
                'visibility' => $bot->visibility?->value,
                'theme' => [
                    'primaryColor' => $bot->theme['primary_color'] ?? '#007bff',
                    'backgroundColor' => $bot->theme['background_color'] ?? '#ffffff',
                    'textColor' => $bot->theme['text_color'] ?? '#333333',
                    'borderRadius' => $bot->theme['border_radius'] ?? '8px',
                    'fontFamily' => $bot->theme['font_family'] ?? 'system-ui',
                ],
                'aiModel' => [
                    'name' => $bot->model_ai?->name ?? 'Default',
                    'provider' => $bot->model_ai?->provider ?? 'OpenAI',
                ],
                'toolCallingMode' => $bot->tool_calling_mode?->value ?? 'auto',
                'parameters' => $bot->getEffectiveParameters(),
                'accessType' => $accessType,
                'metadata' => [
                    'createdAt' => $bot->created_at?->toISOString(),
                    'updatedAt' => $bot->updated_at?->toISOString(),
                ]
            ], 'Bot configuration retrieved successfully');

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Validate widget access to bot.
     */
    public function validateAccess(Request $request, string $identifier): JsonResponse
    {
        try {
            $hasAccess = false;
            $accessType = 'none';
            $botInfo = null;

            // Try API key access
            if ($request->hasHeader('Authorization')) {
                $apiKey = str_replace('Bearer ', '', $request->header('Authorization'));
                
                if (str_starts_with($apiKey, 'pk_')) {
                    $bot = Bot::where('uuid', $identifier)
                             ->where('api_key', $apiKey)
                             ->active()
                             ->first();
                    
                    if ($bot) {
                        $hasAccess = true;
                        $accessType = 'api_key';
                        $botInfo = [
                            'uuid' => $bot->uuid,
                            'name' => $bot->name,
                            'status' => $bot->status,
                        ];
                    }
                }
            }

            // Try share token access
            if (!$hasAccess) {
                $shareLink = BotShareLink::with(['bot'])
                                       ->where('token', $identifier)
                                       ->active()
                                       ->first();

                if ($shareLink && $shareLink->bot->canBeShared()) {
                    $hasAccess = true;
                    $accessType = 'share_token';
                    $botInfo = [
                        'uuid' => $shareLink->bot->uuid,
                        'name' => $shareLink->bot->name,
                        'status' => $shareLink->bot->status,
                        'expiresAt' => $shareLink->expires_at,
                    ];
                }
            }

            return $this->successResponse([
                'hasAccess' => $hasAccess,
                'accessType' => $accessType,
                'bot' => $botInfo,
            ], $hasAccess ? 'Access granted' : 'Access denied');

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Get widget health status.
     */
    public function health(): JsonResponse
    {
        return $this->successResponse([
            'status' => 'healthy',
            'timestamp' => now()->toISOString(),
            'version' => '1.0.0',
        ], 'Widget API is healthy');
    }
}
