<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON><PERSON>\ChatBot\Http\Controllers\KnowledgeBaseController;
use Modules\ChatBot\Http\Controllers\WidgetIframeController;

// use Modules\ChatBot\Http\Controllers\BotController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/knowledge-base/{id}/download', [KnowledgeBaseController::class, 'download'])
    ->name('knowledge-base.download');

// Widget iframe routes (no auth required)
Route::prefix('widget')->name('widget.')->group(function () {
    Route::get('/', [WidgetIframeController::class, 'index'])->name('iframe');
    Route::get('/health', [WidgetIframeController::class, 'health'])->name('iframe.health');
    Route::get('/config', [WidgetIframeController::class, 'config'])->name('iframe.config');
});
