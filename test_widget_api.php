<?php

/**
 * Test script for Widget API endpoints
 * Run with: php test_widget_api.php
 */

// Configuration
$baseUrl = 'http://localhost/api/v1/widget';
$botUuid = 'a716602f-4ccb-47f8-b24d-2421ea958fe7'; // Customer Support Assistant
$apiKey = null; // Will be fetched from database

// Helper function to make HTTP requests
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true),
        'raw' => $response
    ];
}

// Get API key from database
function getApiKey($botUuid) {
    try {
        $pdo = new PDO('mysql:host=localhost;dbname=procms', 'root', '');
        $stmt = $pdo->prepare("SELECT api_key FROM bots WHERE uuid = ?");
        $stmt->execute([$botUuid]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['api_key'] : null;
    } catch (Exception $e) {
        echo "Database error: " . $e->getMessage() . "\n";
        return null;
    }
}

echo "🚀 Testing Widget API Endpoints\n";
echo "================================\n\n";

// Get API key
echo "📋 Getting API key for bot: $botUuid\n";
$apiKey = getApiKey($botUuid);
if (!$apiKey) {
    echo "❌ Failed to get API key\n";
    exit(1);
}
echo "✅ API Key: " . substr($apiKey, 0, 10) . "...\n\n";

// Test 1: Health Check
echo "🔍 Test 1: Health Check\n";
$response = makeRequest("$baseUrl/health");
echo "Status: {$response['status']}\n";
echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";

// Test 2: Bot Configuration
echo "🔍 Test 2: Bot Configuration\n";
$headers = ["Authorization: Bearer $apiKey", "Content-Type: application/json"];
$response = makeRequest("$baseUrl/bot/$botUuid/config", 'GET', null, $headers);
echo "Status: {$response['status']}\n";
echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";

// Test 3: Validate Access
echo "🔍 Test 3: Validate Access\n";
$response = makeRequest("$baseUrl/bot/$botUuid/validate", 'GET', null, $headers);
echo "Status: {$response['status']}\n";
echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";

// Test 4: Create Conversation
echo "🔍 Test 4: Create Conversation\n";
$conversationData = [
    'bot_uuid' => $botUuid,
    'user_id' => 'test_user_' . time(),
    'title' => 'Test Conversation'
];
$response = makeRequest("$baseUrl/conversations", 'POST', $conversationData, $headers);
echo "Status: {$response['status']}\n";
echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";

if ($response['status'] === 200 && isset($response['body']['data']['conversationId'])) {
    $conversationId = $response['body']['data']['conversationId'];
    
    // Test 5: Send Message
    echo "🔍 Test 5: Send Message\n";
    $messageData = [
        'message' => 'Hello, this is a test message from the widget API!',
        'messageType' => 'text'
    ];
    $response = makeRequest("$baseUrl/conversations/$conversationId/messages", 'POST', $messageData, $headers);
    echo "Status: {$response['status']}\n";
    echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";
    
    // Test 6: Get Messages
    echo "🔍 Test 6: Get Messages\n";
    $response = makeRequest("$baseUrl/conversations/$conversationId/messages", 'GET', null, $headers);
    echo "Status: {$response['status']}\n";
    echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";
    
    // Test 7: Get Conversation Info
    echo "🔍 Test 7: Get Conversation Info\n";
    $response = makeRequest("$baseUrl/conversations/$conversationId", 'GET', null, $headers);
    echo "Status: {$response['status']}\n";
    echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";
}

// Test 8: Invalid API Key
echo "🔍 Test 8: Invalid API Key\n";
$invalidHeaders = ["Authorization: Bearer pk_invalid_key", "Content-Type: application/json"];
$response = makeRequest("$baseUrl/bot/$botUuid/config", 'GET', null, $invalidHeaders);
echo "Status: {$response['status']}\n";
echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";

echo "✅ Widget API testing completed!\n";
