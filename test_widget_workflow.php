<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\WidgetUser;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;

echo "🚀 Testing Widget API Workflow\n";
echo "==============================\n\n";

// Test 1: Get a bot with API key
echo "🔍 Test 1: Get Bot with API Key\n";
$bot = Bot::with('model_ai')->first();
if (!$bot) {
    echo "❌ No bots found\n";
    exit(1);
}

echo "✅ Bot: {$bot->name}\n";
echo "✅ UUID: {$bot->uuid}\n";
echo "✅ API Key: " . substr($bot->api_key, 0, 10) . "...\n";
echo "✅ Status: {$bot->status->value}\n";
echo "✅ Has Greeting: " . ($bot->hasGreetingMessage() ? 'Yes' : 'No') . "\n\n";

// Test 2: Create Widget User
echo "🔍 Test 2: Create Widget User\n";
$widgetUserId = 'test_widget_' . time();
$widgetUser = WidgetUser::createOrGet($widgetUserId, [
    'bot_uuid' => $bot->uuid,
    'source' => 'test',
]);

echo "✅ Widget User ID: {$widgetUser->widget_id}\n";
echo "✅ Database ID: {$widgetUser->id}\n";
echo "✅ Display Name: {$widgetUser->display_name}\n\n";

// Test 3: Create Conversation
echo "🔍 Test 3: Create Conversation\n";
$conversation = Conversation::create([
    'uuid' => \Illuminate\Support\Str::uuid(),
    'bot_id' => $bot->id,
    'owner_id' => $widgetUser->id,
    'owner_type' => WidgetUser::class,
    'title' => "Test Widget Conversation - " . now()->format('Y-m-d H:i:s'),
    'status' => \Modules\ChatBot\Enums\ConversationStatus::ACTIVE,
    'last_message_at' => now(),
]);

echo "✅ Conversation UUID: {$conversation->uuid}\n";
echo "✅ Title: {$conversation->title}\n";
echo "✅ Status: {$conversation->status->value}\n\n";

// Test 4: Create User Message
echo "🔍 Test 4: Create User Message\n";
$userMessage = Message::create([
    'uuid' => \Illuminate\Support\Str::uuid(),
    'conversation_id' => $conversation->id,
    'role' => \Modules\ChatBot\Enums\MessageRole::USER,
    'content' => 'Hello! This is a test message from the widget API.',
    'content_type' => \Modules\ChatBot\Enums\ContentType::TEXT,
    'status' => \Modules\ChatBot\Enums\MessageStatus::COMPLETED,
    'created_at' => now(),
]);

echo "✅ User Message UUID: {$userMessage->uuid}\n";
echo "✅ Content: {$userMessage->content}\n";
echo "✅ Role: {$userMessage->role->value}\n";
echo "✅ Status: {$userMessage->status->value}\n\n";

// Test 5: Generate AI Response
echo "🔍 Test 5: Generate AI Response\n";
try {
    $messageService = app('chatbot.message');
    $aiResponse = $messageService->generateAIResponse($conversation, $userMessage);
    
    if (is_string($aiResponse)) {
        echo "❌ Error: {$aiResponse}\n";
    } else {
        echo "✅ AI Message UUID: {$aiResponse->uuid}\n";
        echo "✅ Status: {$aiResponse->status->value}\n";
        echo "✅ Content: " . ($aiResponse->content ?: 'Processing...') . "\n";
        echo "✅ Model: " . ($aiResponse->model_used ?: 'Not set') . "\n";
    }
} catch (Exception $e) {
    echo "❌ Error generating AI response: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 6: Check Conversation Messages
echo "🔍 Test 6: Check Conversation Messages\n";
$messages = Message::where('conversation_id', $conversation->id)
                  ->orderBy('created_at', 'asc')
                  ->get();

echo "✅ Total Messages: {$messages->count()}\n";
foreach ($messages as $msg) {
    echo "  - {$msg->role->value}: " . substr($msg->content ?: 'Processing...', 0, 50) . "...\n";
    echo "    Status: {$msg->status->value}, Created: {$msg->created_at}\n";
}

echo "\n";

// Test 7: Widget API Response Format
echo "🔍 Test 7: Widget API Response Format\n";
$widgetResponse = [
    'conversationId' => $conversation->uuid,
    'botUuid' => $bot->uuid,
    'userId' => $widgetUserId,
    'bot' => [
        'uuid' => $bot->uuid,
        'name' => $bot->name,
        'logoUrl' => $bot->logo_url,
        'greetingMessage' => $bot->greeting_message,
        'status' => $bot->status->value,
    ],
    'messages' => $messages->map(function ($msg) use ($conversation) {
        return [
            'id' => $msg->uuid,
            'conversationId' => $conversation->uuid,
            'type' => $msg->role === \Modules\ChatBot\Enums\MessageRole::USER ? 'user' : 'bot',
            'content' => $msg->content ?: 'Processing...',
            'timestamp' => $msg->created_at?->toISOString(),
            'status' => $msg->status->value,
            'metadata' => [
                'tokens' => $msg->tokens_used,
                'model' => $msg->model_used,
            ]
        ];
    })->toArray(),
];

echo "✅ Widget Response Structure:\n";
echo json_encode($widgetResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

echo "✅ Widget API workflow testing completed!\n";
echo "📋 Summary:\n";
echo "  - Bot configuration: ✅\n";
echo "  - Widget user management: ✅\n";
echo "  - Conversation creation: ✅\n";
echo "  - Message processing: ✅\n";
echo "  - AI response generation: ✅\n";
echo "  - Response format: ✅\n";
