<?php

use Illuminate\Support\Facades\Route;
use Modules\ChatBot\Http\Controllers\Auth\BotController;
use Modules\ChatBot\Http\Controllers\Auth\BotShareController;
use Modules\ChatBot\Http\Controllers\Auth\ChatController;
use Modules\ChatBot\Http\Controllers\Auth\ConversationController;
use Modules\ChatBot\Http\Controllers\Auth\KnowledgeBaseController as AuthKnowledgeBaseController;
use Modules\ChatBot\Http\Controllers\Auth\MessageController;
use Modules\ChatBot\Http\Controllers\Auth\PromptController;
use Modules\ChatBot\Http\Controllers\Api\WebhookController;
use Modules\ChatBot\Http\Controllers\Api\WidgetController;
use Modules\ChatBot\Http\Controllers\Api\WidgetConversationController;
use Modules\ChatBot\Http\Controllers\Api\WidgetMessageController;
use Modules\ChatBot\Http\Controllers\KnowledgeBaseController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Public API routes (no authentication required)
Route::prefix('v1')->group(function () {

    // Public knowledge bases (read-only access)
    Route::get('knowledge-bases', [KnowledgeBaseController::class, 'index'])->name('api.knowledge-bases.index');
    Route::get('knowledge-bases/{id}', [KnowledgeBaseController::class, 'show'])->name('api.knowledge-bases.show');
    Route::get('knowledge-bases/{id}/download', [KnowledgeBaseController::class, 'download'])->name('api.knowledge-bases.download');

    // Webhook endpoints (no auth required)
    Route::post('webhook/ingest', [WebhookController::class, 'ingest'])->name('api.webhook.ingest');
    Route::post('/webhook/query', [WebhookController::class, 'query'])->name('api.webhook.query');
    Route::get('webhook/health', [WebhookController::class, 'health'])->name('api.webhook.health');

    // Widget API endpoints (API key or share token authentication)
    Route::prefix('widget')->name('widget.')->group(function () {
        // Health check (no auth required)
        Route::get('health', [WidgetController::class, 'health'])->name('health');

        // Protected widget endpoints
        Route::middleware(['widget.auth'])->group(function () {
            // Bot configuration and validation
            Route::get('bot/{identifier}/config', [WidgetController::class, 'getBotConfig'])->name('bot.config');
            Route::get('bot/{identifier}/validate', [WidgetController::class, 'validateAccess'])->name('bot.validate');

            // Conversation management
            Route::post('conversations', [WidgetConversationController::class, 'createOrGet'])->name('conversations.create');
            Route::get('conversations/{uuid}', [WidgetConversationController::class, 'show'])->name('conversations.show');

            // Message management
            Route::get('conversations/{uuid}/messages', [WidgetMessageController::class, 'index'])->name('messages.index');
            Route::post('conversations/{uuid}/messages', [WidgetMessageController::class, 'sendMessage'])->name('messages.send');
        });
    });
});

// Authenticated API routes (user context)
Route::middleware(['auth:api'])->prefix('v1/auth')->name('auth.')->group(function () {
    // User bot management
    Route::get('bots', [BotController::class, 'index'])->name('bot.index');
    Route::post('bots', [BotController::class, 'store'])->name('bot.store');
    Route::get('bots/{id}', [BotController::class, 'show'])->name('bot.show');
    Route::put('bots/{id}', [BotController::class, 'update'])->name('bot.update');
    Route::get('bots/dropdown', [BotController::class, 'dropdown'])->name('bot.dropdown');
    Route::post('bots/upload-avatar', [BotController::class, 'handleLogoUpload'])->name('upload-avatar');

    // Bulk operations
    Route::delete('bots/bulk/delete', [BotController::class, 'bulkDelete'])->name('bot.bulk-delete');
    Route::delete('bots/bulk/force', [BotController::class, 'bulkDestroy'])->name('bot.bulk-destroy');
    Route::put('bots/bulk/restore', [BotController::class, 'bulkRestore'])->name('bot.bulk-restore');

    // Individual operations
    Route::delete('bots/{id}/delete', [BotController::class, 'delete'])->name('bot.delete');
    Route::delete('bots/{id}/force', [BotController::class, 'destroy'])->name('bot.destroy');

    // Restore operations
    Route::put('bots/{id}/restore', [BotController::class, 'restore'])->name('bot.restore');
    Route::put('bots/{id}', [BotController::class, 'update'])->name('bot.update');


    // Bot sharing routes
    Route::prefix('bots/{id}/share')->name('bots.share.')->group(function () {
        Route::post('/', [BotShareController::class, 'shareBot'])->name('create');
        Route::post('/link', [BotShareController::class, 'createShareLink'])->name('link.create');
        Route::get('/', [BotShareController::class, 'getBotShares'])->name('index');
        Route::get('/links', [BotShareController::class, 'getBotShareLinks'])->name('links.index');
        Route::delete('/{shareId}', [BotShareController::class, 'revokeShare'])->name('revoke');
        Route::delete('/links/{linkId}', [BotShareController::class, 'revokeShareLink'])->name('link.revoke');
    });

    // Conversation management

    // Prompt General
    Route::get('bot-general-prompt', [PromptController::class, 'getBotGeneralPrompt'])->name('bot-general-prompt');

    // Knowledge Base management
    Route::prefix('knowledge-bases')->name('knowledge-bases.')->group(function () {
        // Utility routes (must be first - specific paths)
        Route::get('dropdown', [AuthKnowledgeBaseController::class, 'dropdown'])->name('dropdown');
        Route::get('files', [AuthKnowledgeBaseController::class, 'getFiles'])->name('files');

        // Bulk operations (specific paths before {id})
        Route::delete('bulk/delete', [AuthKnowledgeBaseController::class, 'bulkDelete'])->name('bulk-delete');
        Route::delete('bulk/force', [AuthKnowledgeBaseController::class, 'bulkDestroy'])->name('bulk-destroy');
        Route::put('bulk/restore', [AuthKnowledgeBaseController::class, 'bulkRestore'])->name('bulk-restore');
        Route::post('bulk-upload', [AuthKnowledgeBaseController::class, 'bulkUpload'])->name('bulk-upload');

        // Creation routes (specific paths)
        Route::post('text', [AuthKnowledgeBaseController::class, 'storeFromText'])->name('store-text');
        Route::post('file', [AuthKnowledgeBaseController::class, 'storeFromFile'])->name('store-file');

        // File upload management (specific paths)
        Route::post('files/upload', [AuthKnowledgeBaseController::class, 'storeFileUpload'])->name('files.upload');
        Route::delete('files/remove', [AuthKnowledgeBaseController::class, 'removeFileUpload'])->name('files.remove');

        // Custom operations (specific paths)
        Route::post('retrain', [AuthKnowledgeBaseController::class, 'retrain'])->name('retrain');

        // Standard CRUD routes (root level)
        Route::get('/', [AuthKnowledgeBaseController::class, 'index'])->name('index');
        Route::post('/', [AuthKnowledgeBaseController::class, 'store'])->name('store');

        // Individual operations with {id} parameter (must be last)
        Route::get('{id}', [AuthKnowledgeBaseController::class, 'show'])->name('show');
        Route::put('{id}', [AuthKnowledgeBaseController::class, 'update'])->name('update');
        Route::patch('{id}', [AuthKnowledgeBaseController::class, 'update'])->name('update');
        Route::delete('{id}/delete', [AuthKnowledgeBaseController::class, 'delete'])->name('delete');
        Route::delete('{id}/force', [AuthKnowledgeBaseController::class, 'destroy'])->name('destroy');
        Route::put('{id}/restore', [AuthKnowledgeBaseController::class, 'restore'])->name('restore');
   });
});


// Authenticated API routes (user context)
Route::middleware(['auth:api'])->prefix('v1/auth/conversations')->name('conversations.')->group(function () {
    // Conversation management
    Route::get('/', [ConversationController::class, 'index'])->name('index');
    Route::get('/{id}', [ConversationController::class, ''])->name('store');
    Route::post('/', [ConversationController::class, 'createOrUpdateConversation'])->name('store');
    Route::put('{id}', [ConversationController::class, 'update'])->name('update-conversation');
    Route::delete('{id}', [ConversationController::class, 'delete'])->name('delete-conversation');


    // Message management for conversations
    Route::get('{id}/messages', [MessageController::class, 'index'])->name('messages.index');
    Route::post('{id}/messages', [MessageController::class, 'sendAndRespond'])->name('messages.sendAndRespond');
    Route::post('{id}/query', [MessageController::class, 'sendQuery'])->name('messages.sendQuery');
});

// Authenticated API routes (user context)
Route::middleware(['auth:api'])->prefix('v1/auth/chat')->name('chat.')->group(function () {
    // Chat bot listing
    Route::get('/bots', [ChatController::class, 'index'])->name('bots.index');
});

// Public share access routes (no authentication required)
Route::prefix('v1/public/share')->name('public.share.')->group(function () {
    Route::get('/bot/{token}', [BotShareController::class, 'accessSharedBot'])->name('bot.access');
    Route::get('/bot/{token}/info', [BotShareController::class, 'getSharedBotInfo'])->name('bot.info');
});

// Admin API routes can be added later if needed
// Route::middleware(['auth', 'admin'])->prefix('v1/admin')->name('admin.')->group(function () {
//     // Admin bot management routes
// });
