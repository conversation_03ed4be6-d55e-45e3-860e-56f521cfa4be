<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Widget Error</title>
    
    <!-- Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="frame-ancestors *;">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            height: 100%;
            font-family: system-ui, -apple-system, sans-serif;
            background: #f8f9fa;
            color: #333;
        }
        
        .error-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 20px;
        }
        
        .error-content {
            text-align: center;
            max-width: 400px;
        }
        
        .error-icon {
            width: 64px;
            height: 64px;
            background: #ff4757;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        
        .error-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .error-message {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 20px;
        }
        
        .error-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .error-details {
            margin-top: 20px;
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            border-left: 4px solid #ff4757;
            text-align: left;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-content">
            <div class="error-icon">!</div>
            
            <h1 class="error-title">Widget Error</h1>
            
            <p class="error-message">
                <?php echo e($message ?? 'An error occurred while loading the chatbot widget.'); ?>

            </p>
            
            <div class="error-actions">
                <button class="btn btn-primary" onclick="retryLoad()">
                    Thử lại
                </button>
                <button class="btn btn-secondary" onclick="reportError()">
                    Báo lỗi
                </button>
            </div>
            
            <div class="error-details">
                <strong>Thông tin lỗi:</strong><br>
                Timestamp: <?php echo e(now()->toISOString()); ?><br>
                User Agent: <span id="user-agent"></span><br>
                URL: <span id="current-url"></span>
            </div>
        </div>
    </div>

    <script>
        // Fill in error details
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('current-url').textContent = window.location.href;
        
        function retryLoad() {
            window.location.reload();
        }
        
        function reportError() {
            const errorInfo = {
                message: <?php echo json_encode($message ?? 'Unknown error', 15, 512) ?>,
                timestamp: <?php echo json_encode(now()->toISOString(), 15, 512) ?>,
                userAgent: navigator.userAgent,
                url: window.location.href,
                referrer: document.referrer
            };
            
            // You can implement error reporting here
            console.error('Widget Error Report:', errorInfo);
            
            // For now, just show an alert
            alert('Thông tin lỗi đã được ghi lại. Vui lòng liên hệ hỗ trợ kỹ thuật.');
        }
        
        // Notify parent window about error
        if (window.parent !== window) {
            window.parent.postMessage({
                type: 'widget-error',
                data: {
                    message: <?php echo json_encode($message ?? 'Unknown error', 15, 512) ?>,
                    timestamp: <?php echo json_encode(now()->toISOString(), 15, 512) ?>
                }
            }, '*');
        }
    </script>
</body>
</html>
<?php /**PATH C:\Tools\laragon\www\laravel-procms\Modules/ChatBot\resources/views/widget/error.blade.php ENDPATH**/ ?>